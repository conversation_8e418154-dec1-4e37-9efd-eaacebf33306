{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Comprehensive log analysis and correlation dashboard for Countly services with trace correlation, structured log analysis, and real-time log monitoring.", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [{"asDropdown": false, "icon": "external link", "includeVars": true, "keepTime": true, "tags": ["countly"], "targetBlank": true, "title": "Countly Dashboards", "type": "dashboards"}], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 1, "panels": [], "title": "📊 Log Volume & Overview", "type": "row"}, {"datasource": {"type": "loki", "uid": "loki"}, "description": "Total log entries per second across all services", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 100}]}, "unit": "logs"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 1}, "id": 2, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "textMode": "auto"}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "sum(rate({namespace=\"countly\", app=~\"$service_name\"}[${__rate_interval}]))", "refId": "A"}], "title": "📈 Log Rate (logs/sec)", "type": "stat"}, {"datasource": {"type": "loki", "uid": "loki"}, "description": "Error log rate per second", "fieldConfig": {"defaults": {"color": {"fixedColor": "red", "mode": "fixed"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}, "unit": "logs"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 1}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "textMode": "auto"}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "sum(rate({namespace=\"countly\", app=~\"$service_name\"} |= \"ERROR\"[${__rate_interval}]))", "refId": "A"}], "title": "🚨 Error Log Rate", "type": "stat"}, {"datasource": {"type": "loki", "uid": "loki"}, "description": "Log volume trends by service", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "logs"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 4, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "sum(rate({namespace=\"countly\", app=~\"$service_name\"}[${__rate_interval}])) by (app)", "legendFormat": "{{app}}", "refId": "A"}], "title": "📊 Log Volume by Service", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 5, "panels": [], "title": "🔍 Log Level Analysis", "type": "row"}, {"datasource": {"type": "loki", "uid": "loki"}, "description": "Distribution of log levels across services", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"tooltip": false, "vis": false, "legend": false}}, "mappings": [], "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*ERROR.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*WARN.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*INFO.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 10}, "id": 6, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "pieType": "pie", "tooltip": {"mode": "single", "sort": "none"}, "legend": {"displayMode": "visible", "placement": "bottom"}}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "sum(count_over_time({namespace=\"countly\", app=~\"$service_name\"} | json | __error__=\"\" [${__interval}])) by (level)", "legendFormat": "{{level}}", "refId": "A"}], "title": "🥧 Log Level Distribution", "type": "piechart"}, {"datasource": {"type": "loki", "uid": "loki"}, "description": "Log level trends over time", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "logs"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*ERROR.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*WARN.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 16, "x": 8, "y": 10}, "id": 7, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "sum(rate({namespace=\"countly\", app=~\"$service_name\"} |= \"ERROR\"[${__rate_interval}])) by (app)", "legendFormat": "{{app}} ERROR", "refId": "A"}, {"datasource": {"type": "loki", "uid": "loki"}, "expr": "sum(rate({namespace=\"countly\", app=~\"$service_name\"} |= \"WARN\"[${__rate_interval}])) by (app)", "legendFormat": "{{app}} WARN", "refId": "B"}, {"datasource": {"type": "loki", "uid": "loki"}, "expr": "sum(rate({namespace=\"countly\", app=~\"$service_name\"} |= \"INFO\"[${__rate_interval}])) by (app)", "legendFormat": "{{app}} INFO", "refId": "C"}], "title": "📈 Log Level Trends", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 8, "panels": [], "title": "🔗 Trace Correlation & Context", "type": "row"}, {"datasource": {"type": "loki", "uid": "loki"}, "description": "Logs with trace correlation information", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "logs"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 19}, "id": 9, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "sum(rate({namespace=\"countly\", app=~\"$service_name\"} | json | traceId != \"\"[${__rate_interval}])) by (app)", "legendFormat": "{{app}} with traces", "refId": "A"}, {"datasource": {"type": "loki", "uid": "loki"}, "expr": "sum(rate({namespace=\"countly\", app=~\"$service_name\"} | json | traceId = \"\"[${__rate_interval}])) by (app)", "legendFormat": "{{app}} without traces", "refId": "B"}], "title": "🔗 Trace Correlation Coverage", "type": "timeseries"}, {"datasource": {"type": "loki", "uid": "loki"}, "description": "Recent correlated error logs with trace IDs", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "level"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}, {"id": "mappings", "value": [{"options": {"ERROR": {"color": "red", "index": 0}}, "type": "value"}]}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 19}, "id": 10, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "Time"}]}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "{namespace=\"countly\", app=~\"$service_name\"} | json | traceId != \"\" | level = \"ERROR\"", "refId": "A"}], "title": "🔍 Correlated E<PERSON><PERSON> Logs", "type": "logs"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 27}, "id": 11, "panels": [], "title": "🔍 Structured Log Analysis", "type": "row"}, {"datasource": {"type": "loki", "uid": "loki"}, "description": "Top log components generating the most logs", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 100}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 28}, "id": 12, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "Count"}]}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "topk(10, sum(count_over_time({namespace=\"countly\", app=~\"$service_name\"} | json | __error__=\"\" [${__interval}])) by (name, app))", "refId": "A"}], "title": "📊 Top Log Components", "transformations": [{"id": "reduce", "options": {"includeTimeField": false, "mode": "reduceFields", "reducers": ["lastNotNull"]}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"Field": "Component", "Last *": "Count"}}}], "type": "table"}, {"datasource": {"type": "loki", "uid": "loki"}, "description": "Job execution logs from JobServer", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 28}, "id": 13, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "sum(rate({app=\"countly-jobserver\"} |~ \"Completed successfully\"[${__rate_interval}]))", "legendFormat": "Jobs Completed", "refId": "A"}, {"datasource": {"type": "loki", "uid": "loki"}, "expr": "sum(rate({app=\"countly-jobserver\", level=\"ERROR\"}[${__rate_interval}]) or rate({app=\"countly-jobserver\"} |= \"ERROR\"[${__rate_interval}]))", "legendFormat": "<PERSON>", "refId": "B"}], "title": "⚙️ Job Execution Monitoring", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 36}, "id": 14, "panels": [], "title": "📝 Live Log Stream", "type": "row"}, {"datasource": {"type": "loki", "uid": "loki"}, "description": "Real-time log stream with filtering capabilities", "fieldConfig": {"defaults": {"custom": {"wrap": true}}, "overrides": []}, "gridPos": {"h": 15, "w": 24, "x": 0, "y": 37}, "id": 15, "options": {"showTime": true, "showLabels": false, "showCommonLabels": false, "wrapLogMessage": true, "prettifyLogMessage": false, "enableLogDetails": true, "sortOrder": "Descending"}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "{namespace=\"countly\", app=~\"$service_name\"} |= \"$search\"", "refId": "A"}], "title": "📺 Live Log Stream", "type": "logs"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 52}, "id": 16, "panels": [], "title": "🔧 Database & Infrastructure Logs", "type": "row"}, {"datasource": {"type": "loki", "uid": "loki"}, "description": "Database-related error patterns", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 53}, "id": 17, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "sum(rate({namespace=\"countly\", app=~\"$service_name\"} |= \"ERROR\" |~ \"[Dd]atabase|[Mm]ongo\"[${__rate_interval}])) by (app)", "legendFormat": "{{app}} DB Errors", "refId": "A"}, {"datasource": {"type": "loki", "uid": "loki"}, "expr": "sum(rate({namespace=\"countly\", app=~\"$service_name\"} |= \"ERROR\" |~ \"[Cc]lick[Hh]ouse\"[${__rate_interval}])) by (app)", "legendFormat": "{{app}} ClickHouse Errors", "refId": "B"}], "title": "🗄️ Database Error <PERSON>s", "type": "timeseries"}, {"datasource": {"type": "loki", "uid": "loki"}, "description": "Recent database error logs with details", "fieldConfig": {"defaults": {"custom": {"wrap": true}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 53}, "id": 18, "options": {"showTime": true, "showLabels": false, "showCommonLabels": false, "wrapLogMessage": true, "prettifyLogMessage": false, "enableLogDetails": true, "sortOrder": "Descending"}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "{namespace=\"countly\", app=~\"$service_name\"} |= \"ERROR\" |~ \"[Dd]atabase|[Mm]ongo|[Cc]lick[Hh]ouse\"", "refId": "A"}], "title": "🗃️ Recent Database Errors", "type": "logs"}], "refresh": "30s", "schemaVersion": 38, "style": "dark", "tags": ["countly", "logs", "correlation", "debugging"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "loki", "uid": "loki"}, "definition": "label_values(app)", "hide": 0, "includeAll": true, "label": "Service", "multi": true, "name": "service_name", "options": [], "query": {"label": "app", "refId": "LokiVariableQueryEditor-VariableQuery", "stream": "", "type": 1}, "refresh": 2, "regex": "countly.*", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "loki", "uid": "loki"}, "definition": "label_values({app=~\"$service_name\"}, level)", "hide": 0, "includeAll": true, "label": "Log Level", "multi": true, "name": "level", "options": [], "query": {"label": "level", "refId": "LokiVariableQueryEditor-VariableQuery", "stream": "{app=~\"$service_name\"}", "type": 1}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": false, "text": "", "value": ""}, "hide": 0, "label": "Search Text", "name": "search", "options": [{"selected": true, "text": "", "value": ""}], "query": "", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Logs & Correlation Analysis", "uid": "countly-logs-correlation", "version": 1, "weekStart": ""}
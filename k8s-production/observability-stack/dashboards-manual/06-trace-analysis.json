{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Comprehensive distributed tracing analysis for Countly services including span analysis, trace correlation, service dependencies, and performance insights.", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [{"asDropdown": false, "icon": "external link", "includeVars": true, "keepTime": true, "tags": ["countly"], "targetBlank": true, "title": "Countly Dashboards", "type": "dashboards"}], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 1, "panels": [], "title": "🔍 Trace Overview & Metrics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Total spans ingested per second from all services", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100}, {"color": "red", "value": 1000}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 1}, "id": 2, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "textMode": "auto"}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(otelcol_receiver_accepted_spans_total[${__rate_interval}]))", "refId": "A"}], "title": "📊 Span Ingestion Rate", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Total spans exported to Tempo per second", "fieldConfig": {"defaults": {"color": {"fixedColor": "blue", "mode": "fixed"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 1}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "textMode": "auto"}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(otelcol_exporter_sent_spans_total[${__rate_interval}]))", "refId": "A"}], "title": "🎯 Spans Exported to Tempo", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Span ingestion and export trends", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 4, "options": {"legend": {"calcs": ["lastNotNull", "mean"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(otelcol_receiver_accepted_spans_total[${__rate_interval}]))", "legendFormat": "Received", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(otelcol_exporter_sent_spans_total[${__rate_interval}]))", "legendFormat": "Exported", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(otelcol_processor_dropped_spans_total[${__rate_interval}]))", "legendFormat": "Dropped", "refId": "C"}], "title": "📈 Span Processing Pipeline", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 5, "panels": [], "title": "🏗️ Service Dependencies & Architecture", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Request rates by service endpoint from HTTP metrics", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 100}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 10}, "id": 6, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "Request Rate"}]}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "topk(20, sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\"}[${__rate_interval}])) by (service_name, http_route))", "format": "table", "instant": true, "refId": "A"}], "title": "🕸️ Service Request Flow", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value": "Request Rate", "service_name": "Service", "http_route": "Endpoint"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Service error rates from HTTP metrics", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.01}, {"color": "red", "value": 0.05}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 10}, "id": 7, "options": {"legend": {"calcs": ["lastNotNull", "mean"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\",http_status_code=~\"5..\"}[${__rate_interval}])) by (service_name) / sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\"}[${__rate_interval}])) by (service_name)", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "💥 Service Error Rates", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 20}, "id": 8, "panels": [], "title": "⏱️ Performance Analysis", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Response time percentiles by service", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 200}, {"color": "red", "value": 500}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 21}, "id": 9, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "histogram_quantile(0.50, sum(rate(countly_http_server_duration_milliseconds_bucket{service_name=~\"$service_name\"}[${__rate_interval}])) by (service_name, le))", "legendFormat": "{{service_name}} P50", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "histogram_quantile(0.95, sum(rate(countly_http_server_duration_milliseconds_bucket{service_name=~\"$service_name\"}[${__rate_interval}])) by (service_name, le))", "legendFormat": "{{service_name}} P95", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "histogram_quantile(0.99, sum(rate(countly_http_server_duration_milliseconds_bucket{service_name=~\"$service_name\"}[${__rate_interval}])) by (service_name, le))", "legendFormat": "{{service_name}} P99", "refId": "C"}], "title": "📊 Response Time Percentiles", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Slowest operations by service", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 200}, {"color": "red", "value": 500}]}, "unit": "ms"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "P95 Duration"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 21}, "id": 10, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "P95 Duration"}]}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "topk(20, histogram_quantile(0.95, sum(rate(countly_http_server_duration_milliseconds_bucket{service_name=~\"$service_name\"}[${__rate_interval}])) by (service_name, http_route, le)))", "format": "table", "instant": true, "refId": "A"}], "title": "🐌 Slowest Operations", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "le": true}, "indexByName": {}, "renameByName": {"Value": "P95 Duration", "service_name": "Service", "http_route": "Endpoint"}}}], "type": "table"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 29}, "id": 11, "panels": [], "title": "🚨 Error Analysis & Debugging", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Error counts by service and endpoint", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "errors"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 30}, "id": 12, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\",http_status_code=~\"5..\"}[${__rate_interval}])) by (service_name, http_route)", "legendFormat": "{{service_name}} - {{http_route}}", "refId": "A"}], "title": "💥 Errors by Endpoint", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Endpoints with highest error rates", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.01}, {"color": "red", "value": 0.05}]}, "unit": "percentunit"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Error Rate"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 30}, "id": 13, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "Error Rate"}]}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "topk(20, sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\",http_status_code=~\"5..\"}[${__rate_interval}])) by (service_name, http_route) / sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\"}[${__rate_interval}])) by (service_name, http_route))", "format": "table", "instant": true, "refId": "A"}], "title": "🔥 Top Error-Prone Endpoints", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value": "Error Rate", "service_name": "Service", "http_route": "Endpoint"}}}], "type": "table"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 38}, "id": 14, "panels": [], "title": "🔍 Trace Search & Investigation", "type": "row"}, {"datasource": {"type": "tempo", "uid": "tempo"}, "description": "Search and explore traces interactively", "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 14, "w": 24, "x": 0, "y": 39}, "id": 15, "targets": [{"datasource": {"type": "tempo", "uid": "tempo"}, "filters": [{"id": "service-name", "operator": "=~", "scope": "resource", "tag": "service.name", "value": ["countly.*"], "valueType": "string"}], "limit": 20, "queryType": "search", "refId": "A", "tableType": "traces"}], "title": "🔎 Trace Explorer", "type": "tempo"}, {"datasource": {"type": "tempo", "uid": "tempo"}, "description": "Service dependency map visualization", "fieldConfig": {"defaults": {"color": {"mode": "categorical"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 53}, "id": 16, "options": {"edges": {"mainStatUnit": "ops/sec", "secondaryStatUnit": "percentunit"}, "nodes": {"arrowPosition": "End", "displayMode": "stats", "mainStatUnit": "ops/sec", "secondaryStatUnit": "ms"}}, "targets": [{"datasource": {"type": "tempo", "uid": "tempo"}, "filters": [{"id": "service-name", "operator": "=~", "scope": "resource", "tag": "service.name", "value": ["countly.*"], "valueType": "string"}], "queryType": "serviceMap", "refId": "A"}], "title": "🕸️ Service Dependency Map", "type": "nodeGraph"}, {"datasource": {"type": "loki", "uid": "loki"}, "description": "Logs correlated with trace IDs", "fieldConfig": {"defaults": {"custom": {"wrap": true}}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 63}, "id": 17, "options": {"showTime": true, "showLabels": false, "showCommonLabels": false, "wrapLogMessage": true, "prettifyLogMessage": false, "enableLogDetails": true, "sortOrder": "Descending", "dedupStrategy": "none"}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "{namespace=\"countly\", app=~\"$service_name\"} | json | traceId != \"\" | line_format \"{{.time}} [{{.level}}] {{.app}} {{.name | default .msg | default \"\"}} traceId={{.traceId}}\"", "refId": "A"}], "title": "📝 Trace-Correlated Logs", "type": "logs"}, {"datasource": {"type": "tempo", "uid": "tempo"}, "description": "Recent traces for selected services", "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 73}, "id": 18, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "tempo", "uid": "tempo"}, "filters": [{"id": "service-name", "operator": "=", "scope": "resource", "tag": "service.name", "value": ["$service_name"], "valueType": "string"}, {"id": "status-code", "operator": "=", "scope": "span", "tag": "status.code", "value": ["ERROR"], "valueType": "string"}], "limit": 10, "queryType": "search", "refId": "A", "tableType": "traces"}], "title": "💥 Recent Error Traces", "type": "tempo"}, {"datasource": {"type": "tempo", "uid": "tempo"}, "description": "Traces taking longer than 1 second", "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 73}, "id": 19, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "tempo", "uid": "tempo"}, "filters": [{"id": "service-name", "operator": "=", "scope": "resource", "tag": "service.name", "value": ["$service_name"], "valueType": "string"}, {"id": "duration", "operator": ">", "scope": "intrinsic", "tag": "duration", "value": "500ms", "valueType": "duration"}], "limit": 10, "queryType": "search", "refId": "A", "tableType": "traces"}], "title": "🐌 Slow Traces (>500ms)", "type": "tempo"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 83}, "id": 20, "panels": [], "title": "📊 Advanced Trace Analytics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Operation duration percentiles by service", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 200}, {"color": "red", "value": 500}]}, "unit": "ms"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "P99 Duration"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 84}, "id": 21, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "P99 Duration"}]}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "topk(20, histogram_quantile(0.99, sum(rate(countly_http_server_duration_milliseconds_bucket{service_name=~\"$service_name\"}[${__rate_interval}])) by (service_name, http_method, http_route, le)))", "format": "table", "instant": true, "refId": "A"}], "title": "🎯 Operation Duration P99", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "le": true}, "indexByName": {}, "renameByName": {"Value": "P99 Duration", "service_name": "Service", "http_method": "Method", "http_route": "Operation"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "HTTP request traces with context propagation", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 84}, "id": 22, "options": {"legend": {"calcs": ["lastNotNull", "mean"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\",trace_id!=\"\"}[${__rate_interval}])) by (service_name) / sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\"}[${__rate_interval}])) by (service_name)", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "🔗 Trace Context Propagation Rate", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Service-to-service communication latency", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"tooltip": false, "vis": false, "legend": false}, "scaleDistribution": {"type": "linear"}}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 92}, "id": 23, "options": {"calculate": false, "cellGap": 1, "cellValues": {"unit": "ms"}, "color": {"exponent": 0.5, "fill": "dark-orange", "mode": "scheme", "reverse": false, "scale": "exponential", "scheme": "Oranges", "steps": 64}, "exemplars": {"color": "rgba(255,0,255,0.7)"}, "filterValues": {"le": 1e-09}, "legend": {"show": true}, "rowsFrame": {"layout": "auto"}, "tooltip": {"show": true, "yHistogram": false}, "yAxis": {"axisPlacement": "left", "reverse": false, "unit": "short"}}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "histogram_quantile(0.95, sum(rate(countly_http_server_duration_milliseconds_bucket{service_name=~\"$service_name\"}[${__rate_interval}])) by (service_name, http_route, le))", "format": "heatmap", "refId": "A"}], "title": "🔥 Service Latency Heatmap", "type": "heatmap"}, {"datasource": {"type": "loki", "uid": "loki"}, "description": "Trace-related errors and warnings", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 92}, "id": 24, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "sum(rate({namespace=\"countly\", app=~\"$service_name\"} |= \"trace\" |= \"error\"[${__rate_interval}])) by (app)", "legendFormat": "{{app}} trace errors", "refId": "A"}, {"datasource": {"type": "loki", "uid": "loki"}, "expr": "sum(rate({namespace=\"countly\", app=~\"$service_name\"} |~ \"span.*error|trace.*fail\"[${__rate_interval}])) by (app)", "legendFormat": "{{app}} span failures", "refId": "B"}], "title": "⚠️ Trace Processing Errors", "type": "timeseries"}], "refresh": "30s", "schemaVersion": 38, "style": "dark", "tags": ["countly", "traces", "distributed", "performance"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(countly_http_server_duration_milliseconds_count, service_name)", "hide": 0, "includeAll": true, "label": "Service", "multi": true, "name": "service_name", "options": [], "query": {"query": "label_values(countly_http_server_duration_milliseconds_count, service_name)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "/^countly-.*/", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Trace Analysis & Dependencies", "uid": "countly-trace-analysis", "version": 1, "weekStart": ""}
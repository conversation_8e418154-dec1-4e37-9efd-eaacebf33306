{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Comprehensive Node.js runtime performance monitoring including memory, event‑loop, GC and CPU across all Countly services.", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": 37, "links": [{"asDropdown": false, "icon": "external link", "includeVars": true, "keepTime": true, "tags": ["countly"], "targetBlank": true, "title": "Countly Dashboards", "type": "dashboards"}], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 1, "panels": [], "title": "💾 Memory Management", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "RSS (Resident Set Size) memory usage for all Node.js services", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 2000000000}, {"color": "red", "value": 4000000000}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 2, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.1.0", "targets": [{"expr": "process_memory_usage{service_name=~\"$service_name\"}", "legendFormat": "{{service_name}} RSS", "refId": "A"}], "title": "💾 Process Memory Usage (RSS)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "V8 JavaScript heap memory usage and limits", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Total.*/"}, "properties": [{"id": "custom.fillOpacity", "value": 0}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 3, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.1.0", "targets": [{"expr": "process_memory_heap_used{service_name=~\"$service_name\"}", "legendFormat": "{{service_name}} Heap Used", "refId": "A"}, {"expr": "process_memory_heap_total{service_name=~\"$service_name\"}", "legendFormat": "{{service_name}} Heap Total", "refId": "B"}], "title": "🧠 V8 Heap Memory Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Heap utilization percentage across services", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 80}, {"color": "red", "value": 95}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 4, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.1.0", "targets": [{"expr": "process_memory_heap_used{service_name=~\"$service_name\"} / process_memory_heap_total{service_name=~\"$service_name\"} * 100", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "📊 Heap Utilization Percentage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "External memory used by C++ objects bound to JavaScript", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "id": 5, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.1.0", "targets": [{"expr": "process_memory_external{service_name=~\"$service_name\"}", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "🔗 External Memory Usage", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "id": 6, "panels": [], "title": "⚡ Event Loop Performance", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Average event‑loop lag in ms", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 18}, "id": 7, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.1.0", "targets": [{"expr": "nodejs_eventloop_lag_mean_seconds{service_name=~\"$service_name\"} * 1000", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "🔄 Event Loop Lag Mean", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "P99 event‑loop lag in ms", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 18}, "id": 15, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.1.0", "targets": [{"expr": "nodejs_eventloop_lag_p99_seconds{service_name=~\"$service_name\"} * 1000", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "🔄 Event Loop Lag P99", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Mean event‑loop lag (ms) — live view", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "fillOpacity": 20, "lineWidth": 2, "showPoints": "never"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}, "unit": "ms"}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 18}, "id": 16, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "12.1.0", "targets": [{"expr": "rate(nodejs_eventloop_lag_milliseconds_count{service_name=~\"$service_name\"}[$__rate_interval])", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "🔄 Event Loop Lag Live Rate", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Event‑loop utilisation %", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}, "unit": "percent"}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["countly-api"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 18}, "id": 8, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.1.0", "targets": [{"expr": "nodejs_eventloop_utilization_ratio{service_name=~\"$service_name\"} * 100", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "⚡ Event Loop Utilisation", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 26}, "id": 9, "panels": [], "title": "🗑️ Garbage Collection Performance", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Garbage‑collection frequency by type and service", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 27}, "id": 10, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.1.0", "targets": [{"expr": "rate(custom_gc_count_total{service_name=~\"$service_name\"}[${__rate_interval}])", "legendFormat": "{{service_name}} {{gc_type}}", "refId": "A"}], "title": "🗑️ GC Frequency by Type", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Number of GC pauses per second (all pause lengths)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "fillOpacity": 20, "lineWidth": 2, "showPoints": "never"}, "unit": "ops", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 27}, "id": 17, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "12.1.0", "targets": [{"expr": "rate(nodejs_gc_pause_ns_nanoseconds_count{service_name=~\"$service_name\"}[$__rate_interval])", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "🗑️ GC Pauses /s", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Same metric as left panel; keep separately if you’d like a different visualization (e.g. overlap, other y‑axis, etc.).", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "fillOpacity": 20, "lineWidth": 2, "showPoints": "never"}, "unit": "ops"}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 27}, "id": 18, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "12.1.0", "targets": [{"expr": "rate(nodejs_gc_pause_ns_nanoseconds_count{service_name=~\"$service_name\"}[$__rate_interval])", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "🗑️ GC Pauses /s (Alt)", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 35}, "id": 12, "panels": [], "title": "🖥️ CPU & System Performance", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "CPU utilisation in user and system mode", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 36}, "id": 13, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.1.0", "targets": [{"expr": "rate(process_cpu_user_microseconds_total{service_name=~\"$service_name\"}[${__rate_interval}]) / 10000", "legendFormat": "{{service_name}} User", "refId": "A"}, {"expr": "rate(process_cpu_system_microseconds_total{service_name=~\"$service_name\"}[${__rate_interval}]) / 10000", "legendFormat": "{{service_name}} System", "refId": "B"}], "title": "🖥️ CPU Utilisation by Mode", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Combined performance overview", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 50}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Event Loop Lag (ms)"}, "properties": [{"id": "unit", "value": "ms"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Memory (Bytes)"}, "properties": [{"id": "unit", "value": "bytes"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 2000000000}, {"color": "red", "value": 4000000000}]}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 36}, "id": 14, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Event Loop Lag (ms)"}]}, "pluginVersion": "12.1.0", "targets": [{"expr": "process_memory_usage{service_name=~\"$service_name\"}", "format": "table", "instant": true, "refId": "A"}, {"expr": "rate(process_cpu_user_microseconds_total{service_name=~\"$service_name\"}[5m]) / 10000", "format": "table", "instant": true, "refId": "B"}, {"expr": "nodejs_eventloop_lag_mean_seconds{service_name=~\"$service_name\"} * 1000", "format": "table", "instant": true, "refId": "C"}, {"expr": "process_memory_heap_used{service_name=~\"$service_name\"} / process_memory_heap_total{service_name=~\"$service_name\"} * 100", "format": "table", "instant": true, "refId": "D"}], "title": "📊 Performance Summary Table", "transformations": [{"id": "joinByField", "options": {"byField": "service_name", "mode": "outer"}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "application": true, "deployment": true, "instance": true, "job": true, "node_version": true}, "renameByName": {"Value #A": "Memory (Bytes)", "Value #B": "CPU %", "Value #C": "Event Loop Lag (ms)", "Value #D": "Heap Util %", "service_name": "Service"}}}], "type": "table"}], "preload": false, "refresh": "30s", "schemaVersion": 41, "tags": ["countly", "nodejs", "performance", "runtime"], "templating": {"list": [{"current": {"text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(nodejs_eventloop_utilization_ratio, service_name)", "includeAll": true, "label": "Service", "multi": true, "name": "service_name", "options": [], "query": {"query": "label_values(nodejs_eventloop_utilization_ratio, service_name)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "/^countly-.*/", "sort": 1, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Node.js Performance", "uid": "countly-nodejs-performance", "version": 1}
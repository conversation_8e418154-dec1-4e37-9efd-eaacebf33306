{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Comprehensive Kubernetes infrastructure monitoring for Countly services including pod counts, resource requests/limits, CPU throttling, pod restarts, and container health metrics.", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [{"asDropdown": false, "icon": "external link", "includeVars": true, "keepTime": true, "tags": ["countly"], "targetBlank": true, "title": "Countly Dashboards", "type": "dashboards"}], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 1, "panels": [], "title": "🏗️ Pod Overview & Deployment Status", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Current number of running pods per service", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 1}, {"color": "green", "value": 2}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 1}, "id": 2, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "textMode": "auto"}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(kube_pod_status_phase{namespace=\"countly\", phase=\"Running\"})", "refId": "A"}], "title": "🚀 Total Running Pods", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Pods in non-running states", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 3}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 1}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "textMode": "auto"}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(kube_pod_status_phase{namespace=\"countly\", phase!=\"Running\"}) by (namespace)", "refId": "A"}], "title": "⚠️ Non-Running Pods", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Pod count trends by service over time", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 4, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(kube_pod_status_phase{namespace=\"countly\", phase=\"Running\"}) by (pod)", "legendFormat": "{{pod}}", "refId": "A"}], "title": "📊 Pod Count by Service", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Current pod status breakdown by phase", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "color-background", "inspect": false}, "mappings": [{"options": {"Failed": {"color": "red", "index": 3}, "Pending": {"color": "yellow", "index": 1}, "Running": {"color": "green", "index": 0}, "Succeeded": {"color": "blue", "index": 2}, "Unknown": {"color": "orange", "index": 4}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Uptime"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "basic"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Restart Count"}, "properties": [{"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 9}, "id": 5, "options": {"showHeader": true, "sortBy": [{"desc": false, "displayName": "Pod"}]}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "kube_pod_status_phase{namespace=\"countly\"}", "format": "table", "instant": true, "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "time() - kube_pod_start_time{namespace=\"countly\"}", "format": "table", "instant": true, "legendFormat": "", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "kube_pod_container_status_restarts_total{namespace=\"countly\"}", "format": "table", "instant": true, "legendFormat": "", "refId": "C"}], "title": "🏥 Pod Status Matrix", "transformations": [{"id": "joinByField", "options": {"byField": "pod", "mode": "outer"}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "container": true, "endpoint": true, "instance": true, "job": true, "namespace": true, "service": true, "uid": true}, "indexByName": {}, "renameByName": {"Value #A": "Status", "Value #B": "Uptime", "Value #C": "Restart Count", "phase": "Phase", "pod": "Pod"}}}], "type": "table"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 6, "panels": [], "title": "💾 Resource Requests & Limits", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Total CPU resources requested by all pods", "fieldConfig": {"defaults": {"color": {"fixedColor": "blue", "mode": "fixed"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 20}, "id": 7, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "textMode": "auto"}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(kube_pod_container_resource_requests{namespace=\"countly\", resource=\"cpu\"})", "refId": "A"}], "title": "🖥️ Total CPU Requests", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Total memory resources requested by all pods", "fieldConfig": {"defaults": {"color": {"fixedColor": "green", "mode": "fixed"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 20}, "id": 8, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "textMode": "auto"}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(kube_pod_container_resource_requests{namespace=\"countly\", resource=\"memory\"})", "refId": "A"}], "title": "💾 Total Memory Requests", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Resource requests vs limits by service", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "id": 9, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(kube_pod_container_resource_requests{namespace=\"countly\", resource=\"cpu\"}) by (pod)", "legendFormat": "{{pod}} CPU Requests", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(kube_pod_container_resource_limits{namespace=\"countly\", resource=\"cpu\"}) by (pod)", "legendFormat": "{{pod}} CPU Limits", "refId": "B"}], "title": "⚖️ CPU Requests vs Limits", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Detailed resource allocation table by pod and container", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "CPU Request"}, "properties": [{"id": "unit", "value": "short"}, {"id": "custom.displayMode", "value": "basic"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "CPU Limit"}, "properties": [{"id": "unit", "value": "short"}, {"id": "custom.displayMode", "value": "basic"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Memory Request"}, "properties": [{"id": "unit", "value": "bytes"}, {"id": "custom.displayMode", "value": "basic"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Memory Limit"}, "properties": [{"id": "unit", "value": "bytes"}, {"id": "custom.displayMode", "value": "basic"}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 28}, "id": 10, "options": {"showHeader": true, "sortBy": [{"desc": false, "displayName": "Pod"}]}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "kube_pod_container_resource_requests{namespace=\"countly\", resource=\"cpu\"}", "format": "table", "instant": true, "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "kube_pod_container_resource_limits{namespace=\"countly\", resource=\"cpu\"}", "format": "table", "instant": true, "legendFormat": "", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "kube_pod_container_resource_requests{namespace=\"countly\", resource=\"memory\"}", "format": "table", "instant": true, "legendFormat": "", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "kube_pod_container_resource_limits{namespace=\"countly\", resource=\"memory\"}", "format": "table", "instant": true, "legendFormat": "", "refId": "D"}], "title": "📋 Resource Allocation Details", "transformations": [{"id": "joinByField", "options": {"byField": "container", "mode": "outer"}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "endpoint": true, "instance": true, "job": true, "namespace": true, "resource": true, "service": true, "uid": true, "unit": true}, "indexByName": {}, "renameByName": {"Value #A": "CPU Request", "Value #B": "CPU Limit", "Value #C": "Memory Request", "Value #D": "Memory Limit", "container": "Container", "pod": "Pod"}}}], "type": "table"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 38}, "id": 11, "panels": [], "title": "🔥 CPU Throttling & Performance", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "CPU throttling events across all containers", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.1}, {"color": "red", "value": 0.5}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 39}, "id": 12, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "rate(container_cpu_cfs_throttled_seconds_total{namespace=\"countly\", container!=\"POD\", container!=\"\"}[5m]) / rate(container_cpu_cfs_periods_total{namespace=\"countly\", container!=\"POD\", container!=\"\"}[5m])", "legendFormat": "{{pod}}/{{container}}", "refId": "A"}], "title": "🔥 CPU Throttling Rate", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "CPU usage vs requests/limits by container", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.8}, {"color": "red", "value": 1}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 39}, "id": 13, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "rate(container_cpu_usage_seconds_total{namespace=\"countly\", container!=\"POD\", container!=\"\"}[5m]) / on(pod, container) group_left() kube_pod_container_resource_requests{namespace=\"countly\", resource=\"cpu\"}", "legendFormat": "{{pod}}/{{container}} Usage", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "kube_pod_container_resource_requests{namespace=\"countly\", resource=\"cpu\"}", "legendFormat": "{{pod}}/{{container}} Request", "refId": "B"}], "title": "📊 CPU Usage vs Requests", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 47}, "id": 14, "panels": [], "title": "💀 Pod Restarts & Container Failures", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Total container restarts in the last hour", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 48}, "id": 15, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "textMode": "auto"}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(increase(kube_pod_container_status_restarts_total{namespace=\"countly\"}[1h]))", "refId": "A"}], "title": "🔄 Total Restarts (1h)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Containers currently in waiting state", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 3}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 48}, "id": 16, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "textMode": "auto"}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(kube_pod_container_status_waiting{namespace=\"countly\"})", "refId": "A"}], "title": "⏳ Containers Waiting", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Container restart trends over time", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 48}, "id": 17, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "rate(kube_pod_container_status_restarts_total{namespace=\"countly\"}[5m]) * 300", "legendFormat": "{{pod}}/{{container}}", "refId": "A"}], "title": "📈 Container Restart Rate", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Detailed container status and restart information", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "color-background", "inspect": false}, "mappings": [{"options": {"0": {"color": "red", "index": 1, "text": "Not Ready"}, "1": {"color": "green", "index": 0, "text": "Ready"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Restart Count"}, "properties": [{"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}, {"id": "custom.displayMode", "value": "color-background"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Container Age"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "basic"}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 56}, "id": 18, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "Restart Count"}]}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "kube_pod_container_status_ready{namespace=\"countly\"}", "format": "table", "instant": true, "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "kube_pod_container_status_restarts_total{namespace=\"countly\"}", "format": "table", "instant": true, "legendFormat": "", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "time() - kube_pod_container_status_last_terminated_time{namespace=\"countly\"}", "format": "table", "instant": true, "legendFormat": "", "refId": "C"}], "title": "🔍 Container Health Details", "transformations": [{"id": "joinByField", "options": {"byField": "container", "mode": "outer"}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "endpoint": true, "instance": true, "job": true, "namespace": true, "service": true, "uid": true}, "indexByName": {}, "renameByName": {"Value #A": "Ready Status", "Value #B": "Restart Count", "Value #C": "Container Age", "container": "Container", "pod": "Pod"}}}], "type": "table"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 66}, "id": 19, "panels": [], "title": "🛡️ Resource Limits & OOM Protection", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Memory usage compared to limits", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.8}, {"color": "red", "value": 0.95}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 67}, "id": 20, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "container_memory_working_set_bytes{namespace=\"countly\", container!=\"POD\", container!=\"\"} / on(pod, container) group_left() kube_pod_container_resource_limits{namespace=\"countly\", resource=\"memory\"}", "legendFormat": "{{pod}}/{{container}}", "refId": "A"}], "title": "💾 Memory Usage vs Limits", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Out of memory kills detected", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 67}, "id": 21, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "increase(container_oom_events_total{namespace=\"countly\"}[1h])", "legendFormat": "{{pod}}/{{container}} OOM Events", "refId": "A"}], "title": "💀 OOM Kill Events (1h)", "type": "timeseries"}], "refresh": "30s", "schemaVersion": 38, "style": "dark", "tags": ["countly", "kubernetes", "infrastructure", "pods"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(container_memory_usage_bytes{namespace=\"countly\"}, pod)", "hide": 0, "includeAll": true, "label": "Service", "multi": true, "name": "service_name", "options": [], "query": {"query": "label_values(container_memory_usage_bytes{namespace=\"countly\"}, pod)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "/^(countly-[^-]+)-.*/", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(kube_pod_info{namespace=\"countly\",pod=~\"$service_name.*\"}, pod)", "hide": 0, "includeAll": true, "label": "Pod", "multi": true, "name": "pod", "options": [], "query": {"query": "label_values(kube_pod_info{namespace=\"countly\",pod=~\"$service_name.*\"}, pod)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(kube_pod_container_info{namespace=\"countly\"}, container)", "hide": 0, "includeAll": true, "label": "Container", "multi": true, "name": "container", "options": [], "query": {"query": "label_values(kube_pod_container_info{namespace=\"countly\"}, container)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Infrastructure Monitoring & Pod Health", "uid": "countly-infrastructure", "version": 1, "weekStart": ""}
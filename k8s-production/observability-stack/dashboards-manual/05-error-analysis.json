{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Comprehensive error analysis for all Countly services including HTTP error rates, database failures, service errors, and detailed error tracking with log correlation.", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [{"asDropdown": false, "icon": "external link", "includeVars": true, "keepTime": true, "tags": ["countly"], "targetBlank": true, "title": "Countly Dashboards", "type": "dashboards"}], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 1, "panels": [], "title": "💥 Error Rate Overview", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Overall error rate across all Countly services", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.01}, {"color": "red", "value": 0.05}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 0, "y": 1}, "id": 2, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "textMode": "auto"}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(countly_http_server_duration_milliseconds_count{http_status_code=~\"[45]..\",service_name=~\"$service_name\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[$__rate_interval])) / sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[$__rate_interval]))", "refId": "A"}], "title": "🔥 Overall Error Rate", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Total error count in the last hour", "fieldConfig": {"defaults": {"color": {"fixedColor": "red", "mode": "fixed"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 100}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 4, "y": 1}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "textMode": "auto"}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(increase(countly_http_server_duration_milliseconds_count{http_status_code=~\"[45]..\",service_name=~\"$service_name\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[1h]))", "refId": "A"}], "title": "📊 Total Errors (1h)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Error rate trends over time", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.01}, {"color": "red", "value": 0.05}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 16, "x": 8, "y": 1}, "id": 4, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(countly_http_server_duration_milliseconds_count{http_status_code=~\"4..\",service_name=~\"$service_name\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[$__rate_interval])) by (service_name) / sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[$__rate_interval])) by (service_name)", "legendFormat": "{{service_name}} 4xx", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(countly_http_server_duration_milliseconds_count{http_status_code=~\"5..\",service_name=~\"$service_name\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[$__rate_interval])) by (service_name) / sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[$__rate_interval])) by (service_name)", "legendFormat": "{{service_name}} 5xx", "refId": "B"}], "title": "📈 Error Rate Trends", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 5, "panels": [], "title": "🔍 Error Breakdown by Type", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Distribution of HTTP status codes across services", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"tooltip": false, "vis": false, "legend": false}}, "mappings": [], "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*4.*./"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*5.*./"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 10}, "id": 6, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "pieType": "pie", "tooltip": {"mode": "single", "sort": "none"}, "legend": {"displayMode": "visible", "placement": "bottom"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[$__rate_interval])) by (http_status_code)", "legendFormat": "{{http_status_code}}", "refId": "A"}], "title": "🥧 Status Code Distribution", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Top error routes and endpoints", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.01}, {"color": "red", "value": 0.05}]}, "unit": "percentunit"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Error Count"}, "properties": [{"id": "unit", "value": "short"}, {"id": "custom.displayMode", "value": "color-background"}]}]}, "gridPos": {"h": 8, "w": 16, "x": 8, "y": 10}, "id": 7, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "Error Rate"}]}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(countly_http_server_duration_milliseconds_count{http_status_code=~\"[45]..\",service_name=~\"$service_name\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[$__rate_interval])) by (service_name, http_route, http_status_code)", "format": "table", "instant": true, "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[$__rate_interval])) by (service_name, http_route)", "format": "table", "instant": true, "legendFormat": "", "refId": "B"}], "title": "🎯 Top Error Routes", "transformations": [{"id": "joinByField", "options": {"byField": "http_route", "mode": "outer"}}, {"id": "calculateField", "options": {"alias": "Error Rate", "binary": {"left": "Value #A", "operator": "/", "right": "Value #B"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Value #B": true}, "indexByName": {}, "renameByName": {"Value #A": "Error Count", "http_route": "Route", "http_status_code": "Status Code", "service_name": "Service"}}}], "type": "table"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 8, "panels": [], "title": "🗃️ Database & Backend Errors", "type": "row"}, {"datasource": {"type": "loki", "uid": "loki"}, "description": "Database-related errors from application logs", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 19}, "id": 9, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "sum(rate({namespace=\"countly\", app=~\"$service_name\"} |= \"ERROR\" |~ \"[Dd]atabase|[Mm]ongo|[Cc]lick[Hh]ouse\"[$__interval])) by (app)", "legendFormat": "{{app}}", "refId": "A"}], "title": "🗄️ Database Error Rate", "type": "timeseries"}, {"datasource": {"type": "loki", "uid": "loki"}, "description": "Application errors by service from structured logs", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 19}, "id": 10, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "sum(rate({namespace=\"countly\", app=~\"$service_name\"} |= \"ERROR\"[$__interval])) by (app)", "legendFormat": "{{app}}", "refId": "A"}], "title": "📝 Application Error Logs", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 27}, "id": 11, "panels": [], "title": "🔗 Error Correlation & Investigation", "type": "row"}, {"datasource": {"type": "loki", "uid": "loki"}, "description": "Recent error logs with trace correlation", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "level"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}, {"id": "mappings", "value": [{"options": {"ERROR": {"color": "red", "index": 0}, "WARN": {"color": "yellow", "index": 1}}, "type": "value"}]}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 28}, "id": 12, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "Time"}]}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "{namespace=\"countly\", app=~\"$service_name\"} |= \"ERROR\" | json | line_format \"{{.time}} [{{.level}}] {{.app}} {{.name | default .msg}} {{if .traceId}}[trace:{{.traceId}}]{{end}}\"", "refId": "A"}], "title": "🔍 Recent Error Logs", "type": "logs"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 38}, "id": 13, "panels": [], "title": "⚡ Performance Impact Analysis", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Error impact on response times", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 500}, {"color": "red", "value": 1000}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 39}, "id": 14, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "histogram_quantile(0.95, sum(rate(countly_http_server_duration_milliseconds_bucket{http_status_code=~\"[45]..\",service_name=~\"$service_name\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[$__rate_interval])) by (service_name, le))", "legendFormat": "{{service_name}} Error P95", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "histogram_quantile(0.95, sum(rate(countly_http_server_duration_milliseconds_bucket{http_status_code=~\"2..\",service_name=~\"$service_name\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[$__rate_interval])) by (service_name, le))", "legendFormat": "{{service_name}} Success P95", "refId": "B"}], "title": "🐌 Error vs Success Response Times", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Memory usage correlation with error rates", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 2000000000}, {"color": "red", "value": 4000000000}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 39}, "id": 15, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "process_memory_usage_bytes{service_name=~\"$service_name\"}", "legendFormat": "{{service_name}} Memory", "refId": "A"}], "title": "💾 Memory Usage During Errors", "type": "timeseries"}], "refresh": "30s", "schemaVersion": 38, "style": "dark", "tags": ["countly", "errors", "analysis", "debugging"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(countly_http_server_duration_milliseconds_count, service_name)", "hide": 0, "includeAll": true, "label": "Service", "multi": true, "name": "service_name", "options": [], "query": {"query": "label_values(countly_http_server_duration_milliseconds_count, service_name)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "/^countly-.*/", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\"}, http_status_code)", "hide": 0, "includeAll": true, "label": "Status Code", "multi": true, "name": "status_code", "options": [], "query": {"query": "label_values(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\"}, http_status_code)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Error Analysis & Debugging", "uid": "countly-error-analysis", "version": 1, "weekStart": ""}
{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Executive overview of the Countly analytics platform with golden signals, service health, and key performance indicators.", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [{"asDropdown": false, "icon": "external link", "includeVars": true, "keepTime": true, "tags": ["countly"], "targetBlank": true, "title": "Countly Dashboards", "type": "dashboards"}], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 1, "panels": [], "title": "🎯 Golden Signals - Platform Health", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Total request rate across all Countly services", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 1}, "id": 2, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[${__rate_interval}])) by (service_name)", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "🔄 Request Rate (req/s)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "P95 response time across all services", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 200}, {"color": "red", "value": 500}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 1}, "id": 3, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "histogram_quantile(0.95, sum(rate(countly_http_server_duration_milliseconds_bucket{service_name=~\"$service_name\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[${__rate_interval}])) by (service_name, le))", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "⏱️ Response Time P95 (ms)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Error rate across all services (5xx responses)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.01}, {"color": "red", "value": 0.05}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 1}, "id": 4, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\",http_status_code=~\"5..\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[${__rate_interval}])) by (service_name) / sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[${__rate_interval}])) by (service_name)", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "💥 Error Rate (5xx)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Memory usage across all Countly services", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 2}, {"color": "red", "value": 4}]}, "unit": "gigabytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 1}, "id": 5, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "process_memory_usage_bytes{service_name=~\"$service_name\"} / 1024 / 1024 / 1024", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "💾 Memory Usage (GB)", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 6, "panels": [], "title": "🏥 Service Health Status", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Current status of all Countly services", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "color-background", "inspect": false}, "mappings": [{"options": {"0": {"color": "red", "index": 1, "text": "DOWN"}, "1": {"color": "green", "index": 0, "text": "UP"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Status"}, "properties": [{"id": "custom.width", "value": 100}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Memory (GB)"}, "properties": [{"id": "unit", "value": "gigabytes"}, {"id": "custom.displayMode", "value": "basic"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Request Rate"}, "properties": [{"id": "unit", "value": "reqps"}, {"id": "custom.displayMode", "value": "basic"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Error Rate"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "custom.displayMode", "value": "basic"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.01}, {"color": "red", "value": 0.05}]}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "id": 7, "options": {"showHeader": true, "sortBy": [{"desc": false, "displayName": "Service"}]}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "count by (service_name) (countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\"}) > 0", "format": "table", "instant": true, "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "process_memory_usage_bytes{service_name=~\"$service_name\"} / 1024 / 1024 / 1024", "format": "table", "instant": true, "legendFormat": "", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[${__rate_interval}])) by (service_name)", "format": "table", "instant": true, "legendFormat": "", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\",http_status_code=~\"5..\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[${__rate_interval}])) by (service_name) / sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[${__rate_interval}])) by (service_name)", "format": "table", "instant": true, "legendFormat": "", "refId": "D"}], "title": "Service Status Matrix", "transformations": [{"id": "joinByField", "options": {"byField": "service_name", "mode": "outer"}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "application": true, "deployment": true, "instance": true, "job": true, "node_version": true}, "indexByName": {}, "renameByName": {"Value #A": "Status", "Value #B": "Memory (GB)", "Value #C": "Request Rate", "Value #D": "Error Rate", "service_name": "Service"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Event loop performance across all Node.js services", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "id": 8, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "histogram_quantile(0.99, sum(rate(nodejs_eventloop_lag_milliseconds_bucket{service_name=~\"$service_name\"}[${__rate_interval}])) by (service_name, le))", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "🔄 Event Loop Lag P99 (ms)", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 9, "panels": [], "title": "📊 Data Ingestion & Processing", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "OpenTelemetry data ingestion rates", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 19}, "id": 10, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "rate(otelcol_receiver_accepted_spans_total[${__rate_interval}])", "legendFormat": "Spans/sec", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "rate(otelcol_receiver_accepted_metric_points_total[${__rate_interval}])", "legendFormat": "Metrics/sec", "refId": "B"}], "title": "📈 OTLP Data Ingestion Rate", "type": "timeseries"}, {"datasource": {"type": "loki", "uid": "loki"}, "description": "Log ingestion rates from all services", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "logs"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 19}, "id": 11, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "sum(rate({namespace=\"countly\", service_name=~\"$service_name\"}[${__interval}])) by (service_name)", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "📝 Log Ingestion Rate", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Profiling data collection rate", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 19}, "id": 12, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "rate(pyroscope_receive_http_request_duration_seconds_count[${__rate_interval}])", "legendFormat": "Profiles/sec", "refId": "A"}], "title": "🔥 Profile Collection Rate", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 27}, "id": 13, "panels": [], "title": "🔗 Quick Navigation", "type": "row"}, {"datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "basic", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 28}, "id": 14, "options": {"showHeader": true}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "refId": "A"}], "title": "Related Dashboards", "transformations": [{"id": "addField", "options": {"defaults": {"Dashboard": "HTTP Request Performance", "Description": "Detailed HTTP metrics and performance analysis", "Link": "/d/http-performance"}}}], "type": "table"}], "refresh": "30s", "schemaVersion": 38, "style": "dark", "tags": ["countly", "overview", "platform"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(countly_http_server_duration_milliseconds_count, service_name)", "hide": 0, "includeAll": true, "label": "Service", "multi": true, "name": "service_name", "options": [], "query": {"query": "label_values(countly_http_server_duration_milliseconds_count, service_name)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "/^countly-.*/", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Countly Platform Overview", "uid": "countly-platform-overview", "version": 1, "weekStart": ""}
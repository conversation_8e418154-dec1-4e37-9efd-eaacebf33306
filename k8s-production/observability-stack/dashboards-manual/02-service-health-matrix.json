{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Comprehensive health monitoring matrix for all Countly services with resource utilization, availability, and performance indicators.", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [{"asDropdown": false, "icon": "external link", "includeVars": true, "keepTime": true, "tags": ["countly"], "targetBlank": true, "title": "Countly Dashboards", "type": "dashboards"}], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 1, "panels": [], "title": "🎯 Service Availability & Status", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Current availability status of all Countly services", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "color-background", "inspect": false}, "mappings": [{"options": {"0": {"color": "red", "index": 1, "text": "DOWN"}, "1": {"color": "green", "index": 0, "text": "UP"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Uptime %"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "custom.displayMode", "value": "gradient-gauge"}, {"id": "min", "value": 0.99}, {"id": "max", "value": 1}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Memory (GB)"}, "properties": [{"id": "unit", "value": "bytes"}, {"id": "custom.displayMode", "value": "basic"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "CPU %"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "custom.displayMode", "value": "basic"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Error Rate %"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "custom.displayMode", "value": "basic"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.01}, {"color": "red", "value": 0.05}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Event Loop Lag (ms)"}, "properties": [{"id": "unit", "value": "ms"}, {"id": "custom.displayMode", "value": "basic"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 1}, "id": 2, "options": {"showHeader": true, "sortBy": [{"desc": false, "displayName": "Service"}]}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "count by (service_name) (countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\"}) > 0", "format": "table", "instant": true, "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "avg_over_time(count(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\"}) by (service_name)[24h:]) > 0", "format": "table", "instant": true, "legendFormat": "", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "process_memory_usage_bytes{service_name=~\"$service_name\"}", "format": "table", "instant": true, "legendFormat": "", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "rate(process_cpu_user_microseconds_total[5m]) / 10000", "format": "table", "instant": true, "legendFormat": "", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\",http_status_code=~\"5..\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[${__rate_interval}])) by (service_name) / sum(rate(countly_http_server_duration_milliseconds_count{service_name=~\"$service_name\",http_route!~\".*/(v1|v2)/(traces|metrics|logs).*\"}[${__rate_interval}])) by (service_name)", "format": "table", "instant": true, "legendFormat": "", "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "nodejs_eventloop_delay_p99_seconds * 1000", "format": "table", "instant": true, "legendFormat": "", "refId": "F"}], "title": "🏥 Service Health Matrix", "transformations": [{"id": "joinByField", "options": {"byField": "service_name", "mode": "outer"}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "application": true, "deployment": true, "instance": true, "job": true, "node_version": true}, "indexByName": {}, "renameByName": {"Value #A": "Status", "Value #B": "Uptime %", "Value #C": "Memory (GB)", "Value #D": "CPU %", "Value #E": "Error Rate %", "Value #F": "Event Loop Lag (ms)", "service_name": "Service"}}}], "type": "table"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 11}, "id": 3, "panels": [], "title": "📊 Resource Utilization Overview", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Memory usage trends for all services", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 2000000000}, {"color": "red", "value": 4000000000}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "id": 4, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "process_memory_usage_bytes{service_name=~\"$service_name\"}", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "💾 Memory Usage by Service", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "CPU utilization across all services", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}, "id": 5, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "rate(process_cpu_user_microseconds_total{service_name=~\"$service_name\"}[${__rate_interval}]) / 10000", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "🖥️ CPU Utilization by Service", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 20}, "id": 6, "panels": [], "title": "⚡ Node.js Performance Metrics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Event loop utilization across all Node.js services", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 21}, "id": 7, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "nodejs_eventloop_utilization{service_name=~\"$service_name\"} * 100", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "🔄 Event Loop Utilization", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Garbage collection frequency by service", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 21}, "id": 8, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "rate(nodejs_gc_count_total{service_name=~\"$service_name\"}[${__rate_interval}])", "legendFormat": "{{service_name}} ({{gc_type}})", "refId": "A"}], "title": "🗑️ Garbage Collection Rate", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Heap memory utilization percentage", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 80}, {"color": "red", "value": 95}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 21}, "id": 9, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "process_memory_heap_used_bytes{service_name=~\"$service_name\"} / process_memory_heap_total_bytes{service_name=~\"$service_name\"} * 100", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "🧠 Heap Utilization", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 29}, "id": 10, "panels": [], "title": "🔔 Alert Status & Health Checks", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Current alert status for all services", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "color-background", "inspect": false}, "mappings": [{"options": {"0": {"color": "green", "index": 0, "text": "OK"}, "1": {"color": "yellow", "index": 1, "text": "WARNING"}, "2": {"color": "red", "index": 2, "text": "CRITICAL"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 2}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 30}, "id": 11, "options": {"showHeader": true}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "ALERTS{alertname=~\".*countly.*\"}", "format": "table", "instant": true, "legendFormat": "", "refId": "A"}], "title": "🚨 Active Alerts", "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Service dependency health check", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "color-background", "inspect": false}, "mappings": [{"options": {"0": {"color": "red", "index": 1, "text": "FAIL"}, "1": {"color": "green", "index": 0, "text": "PASS"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 30}, "id": 12, "options": {"showHeader": true}, "pluginVersion": "10.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "up{job=~\"prometheus|loki|tempo|pyroscope\"}", "format": "table", "instant": true, "legendFormat": "", "refId": "A"}], "title": "🔗 Dependency Health", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "instance": true}, "indexByName": {}, "renameByName": {"Value": "Status", "job": "Component"}}}], "type": "table"}], "refresh": "30s", "schemaVersion": 38, "style": "dark", "tags": ["countly", "health", "services"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(process_memory_usage_bytes, service_name)", "hide": 0, "includeAll": true, "label": "Service", "multi": true, "name": "service_name", "options": [], "query": {"query": "label_values(process_memory_usage_bytes, service_name)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "/^countly-.*/", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Service Health Matrix", "uid": "countly-service-health", "version": 1, "weekStart": ""}